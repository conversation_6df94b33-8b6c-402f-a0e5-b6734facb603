body {
  font-family: sans-serif;
  background: #f3f4f6;
  margin: 0;
  padding: 0;
}
header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  position: fixed;
  width: 100%;
  z-index: 50;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}
.header-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}
.logo {
  font-size: 2rem;
  font-weight: bold;
  color: #dc2626;
  text-decoration: none;
}
.logo span {
  color: #222;
}
nav {
  display: none;
}
.nav-links {
  display: flex;
  gap: 32px;
}
.nav-link {
  color: #222;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}
.nav-link:hover {
  color: #dc2626;
}
.hamburger {
  display: block;
  background: none;
  border: none;
  font-size: 2rem;
  color: #222;
  cursor: pointer;
}
.hamburger-menu {
  display: none;
}
.mobile-menu {
  display: none;
  position: fixed;
  inset: 0;
  background: #fff;
  z-index: 100;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.mobile-menu.active {
  display: flex;
}
.mobile-menu a {
  font-size: 2rem;
  color: #222;
  text-decoration: none;
  margin: 16px 0;
  transition: color 0.2s;
}
.mobile-menu a:hover {
  color: #dc2626;
}
.close-menu {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 2rem;
  color: #222;
  cursor: pointer;
}
@media (min-width: 768px) {
  nav {
      display: block;
  }
  .hamburger {
      display: none;
  }
  .mobile-menu {
      display: none !important;
  }
}
.hero-image {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: 64px;
  background: #222 url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80') center/cover no-repeat;
}
.hero-content {
  color: #fff;
  max-width: 700px;
}
.hero-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 24px;
}
.hero-desc {
  font-size: 1.25rem;
  margin-bottom: 32px;
}
.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}
.hero-btn, .hero-btn-outline {
  padding: 16px 32px;
  border-radius: 9999px;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  transition: background 0.2s, color 0.2s;
}
.hero-btn {
  background: #dc2626;
  color: #fff;
  border: none;
}
.hero-btn:hover {
  background: #b91c1c;
}
.hero-btn-outline {
  background: transparent;
  border: 2px solid #fff;
  color: #fff;
}
.hero-btn-outline:hover {
  background: #fff;
  color: #222;
}
@media (min-width: 640px) {
  .hero-buttons {
      flex-direction: row;
  }
}
section {
  padding: 64px 0;
}
.section-title {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 48px;
}
.why-choose {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
}
.why-card {
  background: #f3f4f6;
  padding: 32px;
  border-radius: 16px;
  text-align: center;
}
.why-icon {
  background: #fee2e2;
  width: 80px;
  height: 80px;
  margin: 0 auto 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.why-icon i {
  color: #dc2626;
  font-size: 2rem;
}
.why-title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 12px;
}
.why-desc {
  color: #666;
}
@media (min-width: 768px) {
  .why-choose {
      grid-template-columns: repeat(3, 1fr);
  }
}
.courses-title {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}
.courses-desc {
  text-align: center;
  color: #666;
  max-width: 600px;
  margin: 0 auto 48px;
}
.courses-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
}
@media (min-width: 768px) {
  .courses-grid {
      grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .courses-grid {
      grid-template-columns: repeat(3, 1fr);
  }
}
.class-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.class-card img {
  width: 100%;
  height: 192px;
  object-fit: cover;
}
.class-card-content {
  padding: 24px;
}
.class-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.class-tag {
  background: #fee2e2;
  color: #dc2626;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 0.9rem;
  font-weight: bold;
}
.class-time {
  color: #666;
  font-size: 0.9rem;
}
.class-title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 8px;
}
.class-desc {
  color: #666;
  margin-bottom: 16px;
}
.class-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.class-price {
  font-weight: bold;
  color: #dc2626;
}
.class-link {
  color: #dc2626;
  font-weight: bold;
  text-decoration: none;
  transition: color 0.2s;
}
.class-link:hover {
  color: #b91c1c;
}
.courses-btn {
  display: inline-block;
  background: #dc2626;
  color: #fff;
  padding: 16px 32px;
  border-radius: 9999px;
  font-weight: bold;
  text-decoration: none;
  margin-top: 48px;
  transition: background 0.2s;
}
.courses-btn:hover {
  background: #b91c1c;
}
.cta-section {
  background: #dc2626;
  color: #fff;
  text-align: center;
  padding: 64px 0;
}
.cta-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 24px;
}
.cta-desc {
  font-size: 1.25rem;
  margin-bottom: 32px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.cta-btn {
  display: inline-block;
  background: #fff;
  color: #dc2626;
  padding: 16px 32px;
  border-radius: 9999px;
  font-weight: bold;
  text-decoration: none;
  transition: background 0.2s;
}
.cta-btn:hover {
  background: #f3f4f6;
}
footer {
  background: #111827;
  color: #fff;
  padding: 48px 0;
}
.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
}
@media (min-width: 768px) {
  .footer-grid {
      grid-template-columns: repeat(4, 1fr);
  }
}
.footer-logo {
  font-size: 2rem;
  font-weight: bold;
  color: #dc2626;
  margin-bottom: 16px;
}
.footer-logo span {
  color: #fff;
}
.footer-social {
  display: flex;
  gap: 16px;
}
.footer-social a {
  color: #fff;
  text-decoration: none;
  font-size: 1.2rem;
  transition: color 0.2s;
}
.footer-social a:hover {
  color: #ef4444;
}
.footer-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 16px;
}
.footer-links, .footer-hours, .footer-contact {
  list-style: none;
  padding: 0;
  margin: 0;
}
.footer-links li, .footer-hours li, .footer-contact li {
  margin-bottom: 8px;
}
.footer-links a {
  color: #fff;
  text-decoration: none;
  transition: color 0.2s;
}
.footer-links a:hover {
  color: #ef4444;
}
.footer-bottom {
  border-top: 1px solid #1f2937;
  margin-top: 32px;
  padding-top: 32px;
  text-align: center;
  color: #9ca3af;
  font-size: 0.95rem;
}